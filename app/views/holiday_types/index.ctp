<?php
echo $this->element('modules/page_content_header', array(
    'header'   => 'Choose a Holiday Type'
));
?>

<section class="page-content-body js-page-content-body">
    <div class="page-content-body__inner whats-hot-index">
        <?php foreach ($holidayTypes as $h) {
            // Debug what's actually in the view
            if ($h === reset($holidayTypes)) {
                echo "<h3>VIEW DEBUG: First holiday type</h3>";
                echo "<p>MainImage exists: " . (isset($h['MainImage']) ? 'YES' : 'NO') . "</p>";
                if (isset($h['MainImage'])) {
                    echo "<p>MainImage ID: " . (isset($h['MainImage']['id']) ? $h['MainImage']['id'] : 'MISSING') . "</p>";
                    echo "<p>MainImage extension: " . (isset($h['MainImage']['extension']) ? $h['MainImage']['extension'] : 'MISSING') . "</p>";
                } else {
                    echo "<p>Available keys: " . implode(', ', array_keys($h)) . "</p>";
                }
            }

            echo $this->element('modules/tile', array(
                'modifier' => 'float',
                'tileUrl' => Router::url(array(
                    $sectionSlugParam   => $sectionSlug,
                    'section'           => $section,
                    'action'            => 'view',
                    'holiday_type_slug' => $h['HolidayType']['slug']
                )),
                'tileImage' => RESOURCES_HOSTNAME . "/img/uploads/{$h['MainImage']['id']}_crop350x263.{$h['MainImage']['extension']}",
                'tileTitle' => $app->strtotitle($h['HolidayType']['name']),
                'tileSummary' => $text->truncate(strip_tags($h['HolidayType']['summary']), 260, '...', false),

            ));
        } ?>
    </div>
</section>

<?php echo $this->element('section_footer') ?>
